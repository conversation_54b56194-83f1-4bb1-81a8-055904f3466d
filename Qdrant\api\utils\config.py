"""
Configuration utilities for the API.
"""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Constants for vector dimensions
CLIP_DIMENSION = 1024
CLIP_TEXT_DIMENSION = 1024
EFFNET_DIMENSION = 2560

# API configuration
API_BEARER_TOKEN = os.getenv("API_BEARER_TOKEN")

# Qdrant configuration
QDRANT_URL = os.getenv("QDRANT_URL", "https://vectorstore1.maidalv.com:6333")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")
